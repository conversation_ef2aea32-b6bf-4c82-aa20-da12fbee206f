
DIR_NEG_X = 0
DIR_POS_X = 1
DIR_NEG_Z = 2
DIR_POS_Z = 3
DIR_NEG_Y = 4
DIR_POS_Y = 5

BLOCK_SIZE = 100
MAPID_GROUND = 0
MAPID_EARTHCORE  = 1
MAPID_PLANETSPACE = 2
local SECTION_BLOCK = 16

function NeighborBlock(x, y, z, dir)
	if dir==DIR_NEG_X then return x-1,y,z
	elseif dir==DIR_POS_X then return x+1,y,z
	elseif dir==DIR_NEG_Z then return x,y,z-1
	elseif dir==DIR_POS_Z then return x,y,z+1
	elseif dir==DIR_NEG_Y then return x,y-1,z
	else return x,y+1,z end
end

local dirCoord = {-1, 1, -1, 1, -1, 1};
--传个位置的分向量,返回值
function NeighborBlockXYZ(xyz, dir, num)
	if dir < DIR_NEG_X or dir > DIR_POS_Y then
		return xyz;
	end
	return dirCoord[dir + 1] * num + xyz;
end

function NeighborBlock2(x, y, z, dir)
	if dir==DIR_NEG_X then return x+1,y,z
	elseif dir==DIR_POS_X then return x-1,y,z
	elseif dir==DIR_NEG_Z then return x,y,z+1
	elseif dir==DIR_POS_Z then return x,y,z-1
	elseif dir==DIR_NEG_Y then return x,y+1,z
	else return x,y-1,z end
end

function BlockCenterCoord(x, y, z)
	return x*BLOCK_SIZE+BLOCK_SIZE/2, y*BLOCK_SIZE+BLOCK_SIZE/2, z*BLOCK_SIZE+BLOCK_SIZE/2;
end

function BlockBottomCoord(x, y, z)
	return x*BLOCK_SIZE+BLOCK_SIZE/2, y*BLOCK_SIZE, z*BLOCK_SIZE+BLOCK_SIZE/2;
end

function CoordDivBlock(x, y, z)
	x = x or 0;
	y = y or 0;
	z = z or 0;
	return math.floor(x/BLOCK_SIZE), math.floor(y/BLOCK_SIZE), math.floor(z/BLOCK_SIZE);
end

function CoordDivSection(x)
	local cx = x / SECTION_BLOCK;
	if x < 0 then
		cx = cx - 1;
	end
	return cx;
end

function BlockDivSection(x)
	return CoordDivSection(CoordDivBlock(x));
end

function ReverseDirection(dir)
	if dir==DIR_NEG_X then return DIR_POS_X
	elseif dir==DIR_POS_X then return DIR_NEG_X
	elseif dir==DIR_NEG_Z then return DIR_POS_Z
	elseif dir==DIR_POS_Z then return DIR_NEG_Z
	elseif dir==DIR_NEG_Y then return DIR_POS_Y
	else return DIR_NEG_Y end
end

function RotateDir90(dir)
	if dir==DIR_NEG_X then return DIR_NEG_Z
	elseif dir==DIR_POS_X then return DIR_POS_Z
	elseif dir==DIR_NEG_Z then return DIR_POS_X
	elseif dir==DIR_POS_Z then return DIR_NEG_X
	else return dir end
end

function LeftOnPlaceDir(nx,ny,nz, placedir)
	if placedir == DIR_NEG_X then
		return nx,ny,nz+1
	elseif placedir == DIR_POS_X then
		return nx,ny,nz-1
	elseif placedir == DIR_NEG_Z then
		return nx-1,ny,nz
	else
		return nx+1,ny,nz
	end
end

function RightOnPlaceDir(nx,ny,nz, placedir)
	if placedir == DIR_NEG_X then
		return nx,ny,nz-1
	elseif placedir == DIR_POS_X then
		return nx,ny,nz+1
	elseif placedir == DIR_NEG_Z then
		return nx+1,ny,nz
	else
		return nx-1,ny,nz
	end
end

--扔骰子, x/100
function RollPoint(x)
	return math.random() <= (x/100)
end

function GenRandomFloat()
	return math.random()
end

--方块有氧气能燃烧
function CanBlockBurnWithOxygen(world, x, y, z)
	if world:getCurMapID() < MAPID_PLANETSPACE then
		return world:getBlockID(x,y,z) == 0
	else
		return world:getBlockID(x,y,z) == BLOCK_PLANTSPACE_OXYGEN
	end
end

--放一个巨型蘑菇
BigMushroom_Data_Top = {4, 2, 7, 0, 12, 1, 5, 3, 6};
BigMushroom_Data_Negx = {8, -1, 9, 8, -1, 9, 4, 0, 5};
BigMushroom_Data_Posx = {11, -1, 10, 11, -1, 10, 7, 1, 6};
BigMushroom_Data_Negz = {8, -1, 11, 8, -1, 11, 4, 2, 7};
BigMushroom_Data_Posz = {9, -1, 10, 9, -1, 10, 5, 3, 6};

function PlaceBigMushroomBlock(x, y, z, id, data)
	if CurWorld:getBlockID(x,y,z) == 0 then
		CurWorld:setBlockAll(x,y,z,id,data)
	end
end

function CheckBigMushroomSpace(ox, oy, oz, height)
	for i=1, height, 1 do
		if CurWorld:getBlockID(ox,oy+i,oz) > 0 then return false end
	end
	return true
end

function PlaceBigMushroom(ox, oy, oz, height, leaf, leaf_center, stem)

	if not CheckBigMushroomSpace(ox,oy,oz,height) then return end

	--place stem
	for i=0, height-2, 1 do
		CurWorld:setBlockAll(ox, oy+i, oz, stem, 0)
	end

	--place top leaf
	for z=-1, 1, 1 do
		for x=-1, 1, 1 do
			if x==0 and z==0 then
				PlaceBigMushroomBlock(ox,oy+height-1,oz, leaf_center, DIR_POS_Y)
			else
				bdata = BigMushroom_Data_Top[(z+1)*3+x+1+1]
				PlaceBigMushroomBlock(ox+x, oy+height-1, oz+z, leaf, bdata)
			end
		end
	end

	--place negx, posx side
	for y=-1, 1, 1 do
		newy = oy+height-3+y
		for z=-1, 1, 1 do
			if y<=0 and z==0 then
				PlaceBigMushroomBlock(ox-2, newy, oz, leaf_center, DIR_NEG_X)
				PlaceBigMushroomBlock(ox+2, newy, oz, leaf_center, DIR_POS_X)
			else
				bdata = BigMushroom_Data_Negx[(y+1)*3+z+1+1]
				PlaceBigMushroomBlock(ox-2, newy, oz+z, leaf, bdata)

				bdata = BigMushroom_Data_Posx[(y+1)*3+z+1+1]
				PlaceBigMushroomBlock(ox+2, newy, oz+z, leaf, bdata)
			end
		end
	end

	--place negz, posz side
	for y=-1, 1, 1 do
		newy = oy+height-3+y
		for x=-1, 1, 1 do
			if y<=0 and x==0 then
				PlaceBigMushroomBlock(ox, newy, oz-2, leaf_center, DIR_NEG_Z)
				PlaceBigMushroomBlock(ox, newy, oz+2, leaf_center, DIR_POS_Z)
			else			
				bdata = BigMushroom_Data_Negz[(y+1)*3+x+1+1]
				PlaceBigMushroomBlock(ox+x, newy, oz-2, leaf, bdata)

				bdata = BigMushroom_Data_Posz[(y+1)*3+x+1+1]
				PlaceBigMushroomBlock(ox+x, newy, oz+2, leaf, bdata)
			end
		end
	end
end


BigMushroomBrown_Top = 
{
	{-1, 4, 2, 2, 2, 7, -1},
	{0,  8, 8, 8, 8, 8,  1},
	{0,  8, 8, 8, 8, 8,  1},
	{0,  8, 8, 8, 8, 8,  1},
	{0,  8, 8, 8, 8, 8,  1},
	{0,  8, 8, 8, 8, 8,  1},
	{-1, 5, 3, 3, 3, 6, -1}
}
function PlaceBigMushroomBrown(ox, oy, oz, height, leaf, leaf_center, stem)

	if not CheckBigMushroomSpace(ox,oy,oz,height) then return end

	--place stem
	for i=0, height-2, 1 do
		CurWorld:setBlockAll(ox, oy+i, oz, stem, 0)
	end

	--place top leaf
	for z=-3, 3, 1 do
		for x=-3, 3, 1 do
			bdata = BigMushroomBrown_Top[z+4][x+4]
			if bdata == 8 then
				PlaceBigMushroomBlock(ox+x, oy+height-1, oz+z, leaf_center, DIR_POS_Y)
			elseif bdata >= 0 then
				PlaceBigMushroomBlock(ox+x, oy+height-1, oz+z, leaf, bdata)
			end
		end
	end
end

function Utf8StringSub(str, numChar)
	if not str then
        return ""
    end
    local lenInByte = #str
    local count = 0
    local i = 1
    while true do
        local curByte = string.byte(str, i)
        if i > lenInByte then
            break
        end
        local byteCount = 1
        if curByte > 0 and curByte < 128 then
            byteCount = 1
        elseif curByte>=128 and curByte<224 then
            byteCount = 2
        elseif curByte>=224 and curByte<240 then
            byteCount = 3
        elseif curByte>=240 and curByte<=247 then
            byteCount = 4
        else
            break
        end
        i = i + byteCount
        count = count + 1
		
        if numChar == count then
			break
		end
    end

    return string.sub(str, 1, i-1)
end

--width_max：字符串最大宽度，中文2个宽度
function Utf8StringSubByWidth(str, width_max,addDot)
	local lenInByte = #tostring(str)

	if lenInByte <= width_max then
		return str
	end
	if addDot == nil then addDot = true end
	local width = 0
	local allByteCount = 1
	local strLen = 0
	while (allByteCount <= lenInByte) do
		local curByte = string.byte(str, allByteCount)
		local byteCount = 0
		if curByte >= 224 then
			--汉字
			width = width + 2
			byteCount = 3
		else 
			--普通字符
			width = width + 1
			byteCount = 1
		end
		strLen = strLen + 1
		-- 重置下一字节的索引                                              
		allByteCount = allByteCount + byteCount
		if  width >= width_max then
			if addDot then
				return Utf8StringSub(str,strLen).."..."
			else
				return Utf8StringSub(str,strLen)
			end
		end
	end
	return str;
end


function Utf8StringLen(str)
    local lenInByte = #str
    local count = 0
    local i = 1
    while true do
        local curByte = string.byte(str, i)
        if i > lenInByte then
            break
        end
        local byteCount = 1
        if curByte > 0 and curByte < 128 then
            byteCount = 1
        elseif curByte>=128 and curByte<224 then
            byteCount = 2
        elseif curByte>=224 and curByte<240 then
            byteCount = 3
        elseif curByte>=240 and curByte<=247 then
            byteCount = 4
        else
            break
        end
        i = i + byteCount
        count = count + 1
    end

    return count
end


--utf8里中间加入一个字符串
function Utf8StringInsert(str, numChar, char_ )
    local lenInByte = #str
    local count = 0
    local i = 1
    while true do
        local curByte = string.byte(str, i)
        if i > lenInByte then
            break
        end
        local byteCount = 1
        if curByte > 0 and curByte < 128 then
            byteCount = 1
        elseif curByte>=128 and curByte<224 then
            byteCount = 2
        elseif curByte>=224 and curByte<240 then
            byteCount = 3
        elseif curByte>=240 and curByte<=247 then
            byteCount = 4
        else
            break
        end
        i = i + byteCount
        count = count + 1
		
        if  numChar == count then
			break
		end
    end

	local f1 = string.sub(str, 1, i-1);
	local f2 = string.sub(str, i);
    return f1 .. char_ .. f2
end

function ClientNeedDevelopmentMask()
	return false
end

local function IsParamNumber(...)
	local param = {...};
	if next(param) == nil then
		return false;
	end
	local num = 0;
	for k, v in pairs(param) do
		num = num + 1;
		if type(v) ~= "number" then
			return false;
		end
	end
	return num == #param;
end

function Direction2PitchYaw(x, y, z)
	if not IsParamNumber(x, y, z) then
		return 0, 0;
	end
    local yaw = math.atan2(-x, -z);
    yaw = math.deg(yaw);
    local r = math.sqrt(x * x, z * z);
    local pitch = math.atan2(y, r);
    pitch = -math.deg(pitch);
    return yaw, pitch;
end

function PitchYaw2Direction(yaw, pitch)
	if not IsParamNumber(yaw, pitch) then
		return 0, 0, 0;
	end
	yaw = math.rad(yaw);
	pitch = math.rad(pitch);
	local y = -math.sin(pitch);
	local r = math.cos(pitch);
	local x = -r * math.sin(yaw);
	local z = -r * math.cos(yaw);
	return x, y, z;
end

function WrapAngleTo180(a)
	if not IsParamNumber(a) then
		return a;
	end
	local da = math.fmod(a, 360);
	if da >= 180 then
		da = da - 360;
	end
	if da < -180 then
		da = da + 180;
	end
	return da;
end

function math.clamp(value, min, max)
	if not IsParamNumber(value, min, max) then
		return value;
	end
	if value < min then
		return min;
	elseif value > max then
		return max;
	else
		return value;
	end
end

function math.lerp(from, to, t)
	if not IsParamNumber(from, to, t) then
		return from;
	end
	t = math.clamp(t, 0, 1);
	return from * (1 - t) + to * t;
end

--比较两个浮点值是否相近（大概小数点后六位得相同）
function math.approximately(a, b)
	if not IsParamNumber(a, b) then
		return false;
	end
	return math.abs(b - a) < math.max(0.000001 * math.max(math.abs(a), math.abs(b)), 1.17549435e-38 * 8);
end

function WrapAngleToSameSign(dstAngle, srcAngle)
	local absAngle = math.abs(dstAngle - srcAngle)
	if absAngle < 180 then return dstAngle end

	if dstAngle > 0 and srcAngle < 0 then 
		dstAngle = dstAngle - 360
	end
	if dstAngle < 0 and srcAngle > 0 then 
		dstAngle = dstAngle + 360
	end
	return dstAngle
end

-- 根据玩法模式动态设置体力消耗配置（需要云服也执行）
function RefreshStrengthConsumptionCfg()
	if not LuaConstants or not WorldMgr then
		return
	end

	local modeIdx = 1;
	if WorldMgr:isEasyMode() then
		modeIdx = 2;
	elseif WorldMgr:isSurviveMode() then
		modeIdx = 1;
	end

	local list = LuaConstants:get().strengthConsumptionCfg
	for i, cfg in ipairs(list) do
		if modeIdx == i then
			local c = LuaInterface:get_lua_const()
			for _k, _v in pairs(cfg) do
				c[_k] = _v;
				--MiniLog("RefreshStrengthConsumptionCfg：", _k, _v, modeIdx == 1 and "default" or "easy" );
			end
		end
	end

	local list2 = LuaConstants:get().thirstConsumptionCfg
	for i, cfg in ipairs(list2) do
		if modeIdx == i then
			local c = LuaInterface:get_lua_const()
			for _k, _v in pairs(cfg) do
				c[_k] = _v;
				--MiniLog("RefreshStrengthConsumptionCfg：", _k, _v, modeIdx == 1 and "default" or "easy" );
			end
		end
	end
end

-- 流星雨刷新概率，第一个是单颗流星，第二个是流星雨的概率
g_meteorShowerGenRateDef = {
	90, 90,
}

function GetMeteorShowerGenRateDef()
	if not g_meteorShowerGenRateDef or #g_meteorShowerGenRateDef < 1 then
		return ""
	end

	local res = g_meteorShowerGenRateDef[1]
	for index = 2, #g_meteorShowerGenRateDef do
		res = res .. "," .. g_meteorShowerGenRateDef[index]
	end

	return res
end


-- 计算工具修理消耗
function GetToolRepairCost(itemId, duration, maxDuration)
	local cost = {}
	local tooldef = ToolDefCsv:get(itemId)
	if not tooldef then
		return cost
	end

	local percent = 1 - (duration / maxDuration)

	for i = 1, 6 do
		local costid = tooldef.RepairId[i - 1]
		local costnum = tooldef.RepairAmount[i - 1]
		if costid > 0 and costnum > 0 then
			table.insert(cost, {id = costid, num = math.ceil(costnum * percent)})
		end
	end

	return cost
end

-- 获取工具修理消耗字符串
function GetToolRepairCostStr(itemId, duration, maxDuration)
	local cost = GetToolRepairCost(itemId, duration, maxDuration)
	local str = ""
	for i, v in ipairs(cost) do
		str = str .. v.id .. ":" .. v.num .. ","
	end
	return str
end

function GetMaxDurationAfterRepair(itemId, duration, maxDuration)
	local tooldef = ToolDefCsv:get(itemId)
	if not tooldef then
		return 0
	end

	local reduce = maxDuration - duration
	local decrease = math.ceil(tooldef.RepairDecresePercent * reduce)
	return maxDuration - decrease
end

-- 卸载枪械配件
-- 枪械道具的初始位置 gun_gridindex
-- 配件道具的索引 com_index
-- 卸载后移动到的位置 moveto_gridindex
function UnEquipGunComponent(player, gun_gridindex, com_index, moveto_gridindex)
	local playeruin = player:getUin()
	MiniLog("UnEquipGunComponent:UnEquipGunComponent", playeruin, gun_gridindex, com_index)
	local backpack = player:getBackPack()
	if not backpack then
		MiniLog("UnEquipGunComponent:backpack is nil", playeruin)
		return
	end

	local gun_grid = backpack:index2Grid(gun_gridindex)
	if not gun_grid then
		MiniLog("UnEquipGunComponent:gun_grid is nil", playeruin, gun_gridindex)
		return
	end

	local gun_itemid = gun_grid:getItemID()
	local gundef = DefMgr:getGunDef(gun_itemid)
	if not gundef then
		MiniLog("UnEquipGunComponent:gundef is nil", playeruin, gun_gridindex)
		return
	end

	local com_itemid = gun_grid:getKVData("slot" .. com_index)
	if not com_itemid or com_itemid == 0 then
		MiniLog("UnEquipGunComponent:com_itemid is 0", playeruin, gun_gridindex, com_index)
		return
	end

	if moveto_gridindex <= 0 then
		MiniLog("UnEquipGunComponent:moveto_gridindex <= 0", playeruin, gun_gridindex, com_index, moveto_gridindex)
		return
	end

	local moveto_grid = backpack:index2Grid(moveto_gridindex)
	if not moveto_grid then
		MiniLog("UnEquipGunComponent:moveto_grid is nil", playeruin, moveto_gridindex)
		return
	end

	-- 如果目标位置有道具，则寻找空格
	local moveto_itemid = moveto_grid:getItemID()
	if moveto_itemid ~= 0 then
		moveto_gridindex = -1
	end

	local data = GridCopyData()
	data.resid = com_itemid
	data.num = 1
	data.duration = gun_grid:getKVData("duration" .. com_index)
	data.maxduration = gun_grid:getKVData("maxDuration" .. com_index)
	local num = 0
	if moveto_gridindex > 0 then
		backpack:replaceItem_byGridCopyData(data, moveto_gridindex)
		num = 1
	else
		num = backpack:addItem_byGridCopyData(data, 2)
	end

	if num ~= 1 then
		return false
	end

	gun_grid:setKVData("slot" .. com_index, 0)
	gun_grid:setKVData("duration" .. com_index, 0)
	gun_grid:setKVData("maxDuration" .. com_index, 0)
	backpack:afterChangeGrid(gun_gridindex)

	player:notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 40805)
	MiniLog("UnEquipGunComponent:success", playeruin, gun_gridindex, com_index)

	-- 检查弹夹及子弹超出的部分处理
	local gun_logical = player:getGunLogical()
	if not gun_logical then
		return false
	end

	local cur_magazine = gun_logical:getMagazine()
	local max_magazine = gun_logical:getMaxMagazines()
	if cur_magazine > max_magazine and gundef.NeedBullet > 0 then
		local surplus = cur_magazine - max_magazine
		gun_grid:setUserDataInt(max_magazine)
		gun_logical:setMagazine(max_magazine)

		-- 返还子弹
		player:socgainItems(gundef.BulletID, surplus, 2, true)
	end

	local params = {gun_gridindex = gun_gridindex}
	SandboxLuaMsg.sendToClient(playeruin, _G.SANDBOX_LUAMSG_NAME.CLOUD.GUN_COMPONENT_CHANGE_TOCLIENT, params)

	if CurMainPlayer == player then
		OnGunComponentChange(player, gun_gridindex)
	end
	return true
end

-- 装备枪械配件 
-- 配件道具的初始位置 origin_gridindex
-- 枪械道具的位置 target_gridindex
-- 鼠标上道具索引 cur_index
-- 如果装配失败 需要将道具移回原位
function EquipGunComponent(player, origin_gridindex, target_gridindex, cur_index)
	local playeruin = player:getUin()
	MiniLog("EquipGunComponent:EquipGunComponent", playeruin, origin_gridindex, target_gridindex, cur_index)
	local backpack = player:getBackPack()
	if not backpack then
		MiniLog("EquipGunComponent:backpack is nil", playeruin)
		return false
	end

	-- 检查目标道具是不是枪械
	local target_grid = backpack:index2Grid(target_gridindex)
	local target_itemid = target_grid:getItemID()
	local target_slot_num = DefMgr:getGunSlotNum(target_itemid)
	if target_slot_num <= 0 then
		MiniLog("EquipGunComponent:target_slot_num <= 0", playeruin, target_gridindex)
		backpack:moveItem(cur_index, origin_gridindex)
		return false
	end

	local gundef = DefMgr:getGunDef(target_itemid)
	if not gundef then
		MiniLog("EquipGunComponent:gundef is nil", playeruin, target_gridindex)
		backpack:moveItem(cur_index, origin_gridindex)
		return false
	end

	-- 检查配件类型
	local com_grid = backpack:index2Grid(cur_index)
	local com_itemid = com_grid:getItemID()
	local comdef = DefMgr:getGunComponentDef(com_itemid)
	if not comdef then
		MiniLog("EquipGunComponent:comdef is nil", playeruin, cur_index)
		backpack:moveItem(cur_index, origin_gridindex)
		return false
	end

	-- 检查配件类型是否支持
	local support_comtype = gundef:GetSupportSlotType(comdef.SlotType)
	if support_comtype == 0 then
		MiniLog("EquipGunComponent:support_comtype is 0", playeruin, cur_index)
		backpack:moveItem(cur_index, origin_gridindex)
		return false
	end

	-- 检查是否有同类型配件
	local tb_unequip_com = {}
	for i = 1, target_slot_num do
		local ccitemid = target_grid:getKVData("slot" .. i)
		if ccitemid > 0 then
			local need_unequip = false
			if ccitemid == com_itemid then
				need_unequip = true		
			else
				local comdef2 = DefMgr:getGunComponentDef(ccitemid)
				if comdef2 then
					local support_comtype2 = gundef:GetSupportSlotType(comdef2.SlotType)
					if support_comtype2 == support_comtype then
						need_unequip = true
					end
				end
			end
			if need_unequip then
				local iteminfo = {index=i, itemid=ccitemid, }
				iteminfo.duration = target_grid:getKVData("duration" .. i)
				iteminfo.maxDuration = target_grid:getKVData("maxDuration" .. i)
				table.insert(tb_unequip_com, iteminfo)

				target_grid:setKVData("slot" .. i, 0)
				target_grid:setKVData("duration" .. i, 0)
				target_grid:setKVData("maxDuration" .. i, 0)
				backpack:afterChangeGrid(target_gridindex)
			end
		end
	end

	-- 将配件移到枪械上
	local success = false
	for i = 1, target_slot_num do
		local ccitemid = target_grid:getKVData("slot" .. i)
		if ccitemid == 0 then
			target_grid:setKVData("slot" .. i, com_itemid)
			target_grid:setKVData("duration" .. i, com_grid:getDuration())
			target_grid:setKVData("maxDuration" .. i, com_grid:getMaxDuration())
			backpack:afterChangeGrid(target_gridindex)
			success = true
			break
		end
	end

	if not success then
		MiniLog("EquipGunComponent:success is false", playeruin, origin_gridindex, target_gridindex, cur_index)
		for _, iteminfo in ipairs(tb_unequip_com) do
			target_grid:setKVData("slot" .. iteminfo.index, iteminfo.itemid)
			target_grid:setKVData("duration" .. iteminfo.index, iteminfo.duration or 0)
			target_grid:setKVData("maxDuration" .. iteminfo.index, iteminfo.maxDuration or 0)
		end
		backpack:afterChangeGrid(target_gridindex)
		backpack:moveItem(cur_index, origin_gridindex)
		return false
	end

	-- 删除配件道具
	backpack:removeItem(cur_index, 1)

	-- 添加卸载的配件
	for i, iteminfo in ipairs(tb_unequip_com) do
		local data = GridCopyData()
		data.resid = iteminfo.itemid
		data.num = 1
		data.duration = iteminfo.duration
		data.maxduration = iteminfo.maxDuration
		local num = backpack:addItem_byGridCopyData(data, 2)
		MiniLog("EquipGunComponent:addItem_byGridCopyData", player:getUin(), iteminfo.itemid, iteminfo.index, iteminfo.duration, iteminfo.maxDuration, num)
	end

	MiniLog("EquipGunComponent:success", player:getUin(), origin_gridindex, target_gridindex, cur_index)

	local params = {gun_gridindex = target_gridindex, player_uin = playeruin}
	SandboxLuaMsg.sendToClient(playeruin, _G.SANDBOX_LUAMSG_NAME.CLOUD.GUN_COMPONENT_CHANGE_TOCLIENT, params)

	if CurMainPlayer == player then
		OnGunComponentChange(player, target_gridindex)
	end
	return true
end

function OnGunComponentChange(player, gun_gridindex)
	MiniLog("OnGunComponentChange:OnGunComponentChange", player:getUin(), gun_gridindex)
	local itemid = player:getBackPack():getGridItem(gun_gridindex)
	if player then
		player:UpdateGunComponentModels(itemid)
	end
end

function NotifyPlayerHurt(uin, attacker_uin, x, y, z, hurt)
	if _G.IsServerBuild then
		local params = {uin = uin, attacker_uin = attacker_uin, x = x, y = y, z = z, hurt = hurt}
		SandboxLuaMsg.sendToClient(uin, _G.SANDBOX_LUAMSG_NAME.CLOUD.PLAYER_HURT_TOCLIENT, params)
	else
		ProcessPlayerHurt(attacker_uin, x, y, z, hurt)
	end
end

-- 客户端
function ProcessPlayerHurt(attacker_uin, x, y, z, hurt)
	local playermainCtrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if not playermainCtrl then
		return
	end
	playermainCtrl:OnPlayerHurt(attacker_uin, x, y, z, hurt)
end

-- 客户端登陆结束回调
function OnMobileLoginRet(code, msg, result)
	local soclobby_ctrl = GetInst("MiniUIManager"):GetCtrl("soclobby")
    if not soclobby_ctrl then
        return
    end

	soclobby_ctrl:OnMobileLoginRet(code, msg, result)
end

-- 客户端获取右侧开火按钮的位置
function GetRightActionBtnPos()
	local playermainCtrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if not playermainCtrl then
		return
	end
	return playermainCtrl:GetRightActionBtnPos()
end