# 方块掉落物品逻辑分析

## 概述

本文档分析了沙盒游戏中玩家挖掘方块和NPC挖掘方块的掉落物品逻辑，检查两者是否使用相同的掉落规则。

## 核心发现

**结论：玩家挖掘和NPC挖掘使用不同的掉落逻辑实现，但最终都调用相同的掉落规则系统。**

## 详细分析

### 1. 玩家挖掘方块逻辑

#### 调用链路
1. **协议处理**: `PB_BLOCK_ATTACK_CH` → `MpGameSurviveNetHandler::handleAttackBlock2Host`
2. **状态处理**: `AttackBlockState::attackBlock` → `PlayerControl::attackBlock`
3. **方块破坏**: `ClientPlayer::destroyBlock` → `World::playerDestroyBlock` → `World::destroyBlock`
4. **掉落处理**: `BlockMaterial::dropBlockAsItemWithToolId` → `BlockMaterial::dropBlockAsItem`

#### 关键代码位置
- **客户端发送**: `Source\SandboxGame\Play\gameplay\mpgameplay\MpPlayerControl.cpp:844-859`
- **服务器处理**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:1553-1605`
- **方块破坏**: `Source\SandboxGame\Play\player\ClientPlayer_Interact.cpp:2330-2612`
- **掉落逻辑**: `Source\SandboxEngine\Core\worldData\world.cpp:1542-1582`

#### 掉落参数
```cpp
// 玩家挖掘调用
sucDestroy = m_pWorld->playerDestroyBlock(pos, BLOCK_MINE_TOOLFIT, 
    getLivingAttrib()->getDigProbEnchant(), curToolID, getUin());

// 最终调用
blockmtl->dropBlockAsItemWithToolId(this, pos, blockdata, droptype, 1.0f, useToolId, uin);
```

### 2. NPC挖掘方块逻辑

#### 调用链路
1. **AI行为**: `AIPlayerDigBlock::update` → `AINpcPlayer::getWorld()->destroyBlock`
2. **方块破坏**: `World::destroyBlock` (直接调用，不经过playerDestroyBlock)
3. **掉落处理**: `BlockMaterial::dropBlockAsItemWithToolId` → `BlockMaterial::dropBlockAsItem`

#### 关键代码位置
- **NPC挖掘**: `Source\SandboxGame\Play\player\AIPlayerDigBlock.cpp:233-236`
- **方块破坏**: `Source\SandboxEngine\Core\worldData\world.cpp:1542-1582`

#### 掉落参数
```cpp
// NPC挖掘调用
m_pAINpcPlayer->getWorld()->destroyBlock(blockpos, m_MineType, 
    m_pAINpcPlayer->getLivingAttrib()->getDigProbEnchant());

// 最终调用 (useToolId=0, uin=-1)
blockmtl->dropBlockAsItemWithToolId(this, pos, blockdata, droptype, 1.0f, useToolId, uin);
```

### 3. 掉落规则系统

#### BLOCK_MINE_TYPE 枚举
```cpp
enum BLOCK_MINE_TYPE
{
    BLOCK_MINE_NONE = 0,     // 不掉落
    BLOCK_MINE_NOTOOL,       // 工具不符合  
    BLOCK_MINE_TOOLFIT,      // 空手或者工具符合
    BLOCK_MINE_PRECISE,      // 精准掉落
};
```

#### 核心掉落逻辑
**位置**: `Source\SandboxEngine\Core\blocks\BlockMaterialBase.cpp:1046-1068`

```cpp
void BlockMaterial::dropBlockAsItem(World *pworld, const WCoord &blockpos, 
    int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin)
{
    if(droptype == BLOCK_MINE_NONE) return;
    if(GenRandomFloat() > chance) return;
    if (pworld->IsUGCEditMode()) return;
    
    // 检查方块设置是否允许掉落
    if (pworld->CheckBlockSettingEnable(this, ENABLE_DROPITEM) == 0) return;
    
    // 根据挖掘类型决定掉落物品
    if(droptype == BLOCK_MINE_NOTOOL)
    {
        // 使用HandMineDrops配置
        int itemnum = GetItemNumByOdds(def->HandMineDrops.odds);
        if (itemnum > 0)
        {
            int itemid = def->HandMineDrops.item;
            itemMap.insert(std::make_pair(itemid, itemnum));
        }
    }
    else if(droptype == BLOCK_MINE_TOOLFIT)
    {
        // 使用ToolMineDrops配置
        // 根据工具等级和幸运附魔计算掉落
    }
    // ... 其他掉落逻辑
}
```

## 关键差异分析

### 1. 调用路径差异

| 挖掘类型 | 调用方法 | 传入参数 |
|---------|---------|---------|
| 玩家挖掘 | `World::playerDestroyBlock` | `useToolId`=当前工具ID, `uin`=玩家ID |
| NPC挖掘 | `World::destroyBlock` | `useToolId`=0, `uin`=-1 |

### 2. 工具ID差异

- **玩家挖掘**: 传入实际使用的工具ID (`curToolID`)
- **NPC挖掘**: 传入默认值0 (无工具)

这个差异可能影响：
- 工具等级相关的掉落规则
- 特殊工具的掉落加成
- 精准采集等附魔效果

### 3. 玩家ID差异

- **玩家挖掘**: 传入实际玩家UIN
- **NPC挖掘**: 传入-1 (无玩家)

这个差异可能影响：
- 玩家相关的掉落统计
- 成就系统触发
- 观察者事件的玩家归属

### 4. 挖掘类型 (BLOCK_MINE_TYPE)

两者都可能使用不同的挖掘类型：
- **玩家**: 通常使用 `BLOCK_MINE_TOOLFIT`
- **NPC**: 使用 `m_MineType` (在AI初始化时设定)

## 特殊方块的掉落差异

某些方块重写了 `dropBlockAsItemWithToolId` 方法，可能对工具ID有特殊处理：

### 符文矿石 (BlockMineStoneRune)
**位置**: `Source\SandboxGame\Core\blocks\BlockMineStone.cpp:944-956`

```cpp
void BlockMineStoneRune::dropBlockAsItemWithToolId(World* pworld, const WCoord& blockpos, 
    int blockdata, BLOCK_MINE_TYPE droptype, float chance, int useToolId, int uin)
{
    const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(useToolId);
    if (tooldef && tooldef->Type == 2 && tooldef->Level >= 2) {
        // 只有铜镐以上才能掉符文石
        // 根据工具等级决定掉落品质
    }
}
```

**影响**: NPC挖掘符文矿石时，由于 `useToolId=0`，无法获得符文石掉落。
