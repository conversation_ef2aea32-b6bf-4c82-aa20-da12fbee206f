--[[
    设置高级界面单例数据管理
	Default_GameConfiguration_xxx.Json配置表
	{
		"m_eRealTimeShadows": 0, -- 实时阴影(0关，1开)
		"m_ShadowCfg": {
			"m_nDistance": 0,
			"m_eShadowResolutionLevel": 0,
			"m_nCascadeLevel": 0
		},
		"m_eWaterReflection": 0, -- 水面反射(0关，1开)
		"m_eWaterSurfaceCaustics": 0, -- 水面焦散(0关，1开)
		"m_eFogEffect": 2, -- 雾效(0关，1失效，2线性，4，8)
		"m_eDynamicSky": 0, -- 动态天空(0关，1开)
		"m_eDynamicVegetation": 0, -- 动态植被(0关，1开)
		"m_eVolumetricLights": 1, -- 体积光(0关，1开)
		"m_eAntiAliasing": 0, -- 抗锯齿(0关，1开)
		"m_eLutFilter": 1, -- 颜色滤镜(LUT滤镜)(0关，1开)
		"m_eBloom": 1, -- 泛光(0关，1开)
		"m_bHDR": false, -- HDR(false关，true开)
		"m_eDepthOfField": 0, -- 景深(0关，1开)
		"m_bSSAO": false, -- SSAO(false关，true开)
		"m_eTextureQuality": 0, -- 贴图质量(0低，1中，2高)
		"m_eModelQuality": 0, -- 模型质量(0低，1中，2高)
		"m_eEffectQuality": 0, -- 特效质量(0低，1中，2高)
		"m_eMaterialQuality": 0, -- 材质质量(0低，1中，2高)
		"m_eVisionLevel":4, -- 视野(0近，1中，2远，3更远，4最远)
		"m_visionCfg": {
			"m_level": 0,
			"m_nLodlayer": 0,
			"m_nLayerDistance": 0,
			"m_nLodTrisMin": 0,
			"m_nLodTrisMax": 0,
			"m_nFogDisstanceMin": 0,
			"m_nFogDisstanceMax": 0
		},
		"m_eResolutionLevel": 2, -- 帧率(0低，1中，2高)
		"m_nLimitFrameRate": 30 -- 帧率限制(30，60，120)
	}
	具体注释可以参考GameConfiguration、GameConfigMgr
]]
local gameSetAdvancedManager = Class("gameSetAdvancedManager")
local instance = nil

-- 获取实例
function gameSetAdvancedManager:GetInst()
    if not instance then
        instance = ClassList["gameSetAdvancedManager"].instance()
    end

    return instance
end

-- 初始化
function gameSetAdvancedManager:Init()	
	self.defaultConfigTbl = {
		[self:GetGraphicSettingTypeTbl().low] = "Default_GameConfiguration_Low.json", -- 低配
		[self:GetGraphicSettingTypeTbl().middle] = "Default_GameConfiguration_Medium.json", -- 中配
		[self:GetGraphicSettingTypeTbl().high] = "Default_GameConfiguration_High.json", -- 高配
	}
	self.customGraphicSettingConfigPath = "Custom_GameConfiguration.json"
	self:InitEvent()
	self.bUserClick = false
	self.nAutoSetVDistance = nil
end

function gameSetAdvancedManager:InitEvent()
	SandboxLua.eventDispatcher:CreateEvent(nil, "ENTER_STORE") -- 进入商城
    SandboxLua.eventDispatcher:SubscribeEvent(nil, "ENTER_STORE", function(context)
		GetInst("gameSetAdvancedManager"):SetLocalLight(true) -- 商城默认打开动态光源
    end)

	SandboxLua.eventDispatcher:CreateEvent(nil, "EXIT_STORE") -- 退出商城
    SandboxLua.eventDispatcher:SubscribeEvent(nil, "EXIT_STORE", function(context)
		GetInst("gameSetAdvancedManager"):SetDynamicLights(GetInst("gameSetAdvancedManager"):GetDynamicLights()) -- 恢复动态光源
    end)
end

function gameSetAdvancedManager:IsOpenNew()
	return true
end

function gameSetAdvancedManager:Open()
	GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common", "miniui/miniworld/common_comp"}, "gameSetAdvancedAutoGen")
	GetInst("MiniUIManager"):OpenUI("gameSetAdvanced", "miniui/miniworld/gameSetAdvanced", "gameSetAdvancedAutoGen", {fullScreen = {Type = 'Normal'}})
end

function gameSetAdvancedManager:Close()
	GetInst("MiniUIManager"):CloseUI("gameSetAdvancedAutoGen", true)
end

function gameSetAdvancedManager:HideUI()
	GetInst("MiniUIManager"):HideUI("gameSetAdvancedAutoGen")
end

function gameSetAdvancedManager:isPC()
	return GetClientInfo():isPC()
end

function gameSetAdvancedManager:isMobile()
	return GetClientInfo():isMobile()
end

-- 初始化帧率
function gameSetAdvancedManager:InitFps()
	local hasCustomConfigFile = GameConfigMgr:CheckCustomUserConfigIsExist(self.customGraphicSettingConfigPath)
    local fps_custom = GetIWorldConfig():getGameData("fps_custom")
	if not hasCustomConfigFile or fps_custom == 0 then -- 没有设置过
		-- 初始化给低帧率配置，帧率设置单独处理，不跟画质选择关联
		GetIWorldConfig():setGameData("fps_custom", self:GetFpsTypeTbl().low)
	end
	fps_custom = self:GetFps()
	self:SetFps(fps_custom)
end

-- 初始雾效
function gameSetAdvancedManager:InitFog()
	local fog = self:GetFog()
	if fog == GetInst("gameSetAdvancedManager"):GetFogTypeTbl().none or fog == GetInst("gameSetAdvancedManager"):GetFogTypeTbl().disable then
		fog = GetInst("gameSetAdvancedManager"):GetFogTypeTbl().none
	end
	self:SetFog(fog)
end

-- 初始阴影
function gameSetAdvancedManager:InitShadow(bLoadDefaultCfg)
	local shadow = self:GetShadow()
	if bLoadDefaultCfg then
		local graphic_setting = GetInst("gameSetAdvancedManager"):GetGraphicSetting()
		if graphic_setting == GetInst("gameSetAdvancedManager"):GetGraphicSettingTypeTbl().low then
			shadow = GetInst("gameSetAdvancedManager"):GetSwitchTypeTbl().close
		else
			shadow = GetInst("gameSetAdvancedManager"):GetSwitchTypeTbl().open
		end
	end
	self:SetShadow(shadow)
end

-- 初始水反
function gameSetAdvancedManager:InitReflect(bLoadDefaultCfg)
	local reflect = self:GetReflect()
	if bLoadDefaultCfg then
		local graphic_setting = GetInst("gameSetAdvancedManager"):GetGraphicSetting()
		if graphic_setting == GetInst("gameSetAdvancedManager"):GetGraphicSettingTypeTbl().low or graphic_setting == GetInst("gameSetAdvancedManager"):GetGraphicSettingTypeTbl().middle then
			reflect = GetInst("gameSetAdvancedManager"):GetSwitchTypeTbl().close
		else
			reflect = GetInst("gameSetAdvancedManager"):GetSwitchTypeTbl().open
		end
	end
	self:SetReflect(reflect)
end

-- 初始动态光源
function gameSetAdvancedManager:InitDynamicLights(bLoadDefaultCfg)
	local dynamicLights = self:GetDynamicLights()
	if bLoadDefaultCfg then
		local graphic_setting = GetInst("gameSetAdvancedManager"):GetGraphicSetting()
		if graphic_setting == GetInst("gameSetAdvancedManager"):GetGraphicSettingTypeTbl().low or graphic_setting == GetInst("gameSetAdvancedManager"):GetGraphicSettingTypeTbl().middle then
			dynamicLights = GetInst("gameSetAdvancedManager"):GetSwitchTypeTbl().close
		else
			dynamicLights = GetInst("gameSetAdvancedManager"):GetSwitchTypeTbl().open
		end
	end
	self:SetDynamicLights(dynamicLights)
end

-- 初始视野自动调整
function gameSetAdvancedManager:InitAutoSetViewDistance()
	local hasCustomConfigFile = GameConfigMgr:CheckCustomUserConfigIsExist(self.customGraphicSettingConfigPath)
	if not hasCustomConfigFile then -- 没有设置过
		self:SetAutoSetViewDistance(self:GetSwitchTypeTbl().open)
	end
	if self.nAutoSetVDistance and self.nAutoSetVDistance < self:GetVDistance() then -- 如果游戏中自动调整了视野，重新进入界面需要刷新成自动刷新的值
		self:SetVDistance(self.nAutoSetVDistance)
		self.nAutoSetVDistance = nil
	end
end

function gameSetAdvancedManager:LoadSetFromCurConfig(bLoadDefaultCfg)
	self:InitFps()
	self:InitFog()
	self:InitShadow(bLoadDefaultCfg)
	self:InitReflect(bLoadDefaultCfg)
	self:InitDynamicLights(bLoadDefaultCfg)
	self:InitAutoSetViewDistance()
end

-- 记录玩家点击高级设置界面上的按钮
function gameSetAdvancedManager:UserClick()
	if not self.bUserClick then
		self.bUserClick = true
		if GameConfigMgr.IsRawConfig and GameConfigMgr:IsRawConfig() then
			if GameConfigMgr.SetRawConfigFlag then
				GameConfigMgr:SetRawConfigFlag(false)
			end
		end
	end
end

-- 获取强制更新版本号
function gameSetAdvancedManager:GetForceCheckVer()
	if not isAbroadEvn() then
		return 1 -- 国内
	else
		return 0 -- 海外
	end
end

-- 更新强制更新版本号
function gameSetAdvancedManager:UpdateForceCheckVer()
	if GameConfigMgr.SetConfigVersion then
		GameConfigMgr:SetConfigVersion(self:GetForceCheckVer())
	end
end

-- 是否强制检测画质
function gameSetAdvancedManager:IsForceCheckConfig()
	local cfgVer = 0
	if GameConfigMgr.GetConfigVersion then
		cfgVer = GameConfigMgr:GetConfigVersion()
	else
		return false
	end
	local checkVer = self:GetForceCheckVer()
	if cfgVer < checkVer then
		return true
	end
	return false
end

-- 通过评分获得画面设置等级
function gameSetAdvancedManager:AdjustGraphicSetting()
	-- 【1】流畅【2】普通【3】高级【4】超清
	local graphic_setting = self:GetGraphicSettingTypeTbl().middle
	local qualityValue = GameConfigMgr:GetSettingQualityValueByDevice("")
	if qualityValue == 1 or qualityValue == 2 then
		graphic_setting = self:GetGraphicSettingTypeTbl().low
	elseif qualityValue == 3 then
		graphic_setting = self:GetGraphicSettingTypeTbl().middle
	elseif qualityValue == 4 then
		graphic_setting = self:GetGraphicSettingTypeTbl().high
	end
	self:SetGraphicSetting(graphic_setting)
	self:UpdateForceCheckVer()
	self:SaveCustomConfig()
end

-- 初始化配置
function gameSetAdvancedManager:InitConfig()
    local hasCustomConfigFile = GameConfigMgr:CheckCustomUserConfigIsExist(self.customGraphicSettingConfigPath)
	local urlEncode = function(s)
		s = string.gsub(s, "([^%w%.%- _])", function(c) return string.format("%%%02X", string.byte(c)) end)
	   return string.gsub(s, " ", "+")
	end
	--local deviceModelStr = GameConfigMgr:GetDeviceModelStr()
	--local clientVer = GetClientInfo():GetClientVersionStr()
	if not hasCustomConfigFile then -- 本地没有自定义的配置表
		self:AdjustGraphicSetting()
	else
		self:LoadCustomConfig()
		local bForceCheckConfig = self:IsForceCheckConfig()
		if bForceCheckConfig then
			self:AdjustGraphicSetting()
		end
	end

	

end

-- 读取默认配置
function gameSetAdvancedManager:LoadDefaultConfig(graphic_setting)
	if self.defaultConfigTbl[graphic_setting] then
		GameConfigMgr:LoadGameConfig(self.defaultConfigTbl[graphic_setting])
		self:LoadSetFromCurConfig(true)
	end
end

-- 读取本地自定义配置
function gameSetAdvancedManager:LoadCustomConfig()
	GameConfigMgr:LoadGameConfig(self.customGraphicSettingConfigPath)
	self:LoadSetFromCurConfig()
end

-- 保存本地自定义配置
function gameSetAdvancedManager:SaveCustomConfig()
	GameConfigMgr:SaveGameConfig(self.customGraphicSettingConfigPath)
end

-- 恢复默认配置
function gameSetAdvancedManager:RestoreDefaultConfig(t_DefaultSetData)
	self:SetGraphicSetting(t_DefaultSetData["graphic_setting"])
	self:SetTreeShape(t_DefaultSetData["treeshape"])
	self:SetVibrate(t_DefaultSetData["vibrate"])
	if isAbroadEvn() then
		GetInst("eventMgr"):dispatch("GAME_SET_ADVANCED_REFRESH")
	else
		g_LuaEvent:dispatchEvent(ET.GAME_SET_ADVANCED_REFRESH)
	end
end

-- 是否需要弹切高画质时，弹出警示窗口
function gameSetAdvancedManager:NeedShowHighGraphicTips()
	return false
end

-- 开关类型
function gameSetAdvancedManager:GetSwitchTypeTbl()
	self.switchTypeTbl = self.switchTypeTbl or {
		open = 1, -- 开
		close = 0 -- 关
	}
	return self.switchTypeTbl
end

-------------------------------------- 画质设置 -----------------------------------------------
function gameSetAdvancedManager:GetGraphicSettingTypeTbl()
	self.graphicSettingTypeTbl = self.graphicSettingTypeTbl or {
		low = 0,    -- 低
		middle = 1, -- 中
		high = 2    -- 高
	}
	return self.graphicSettingTypeTbl
end

function gameSetAdvancedManager:GetTransformGraphicSettingTypeTbl()
	self.transformGraphicSettingTypeTbl = self.transformGraphicSettingTypeTbl or {
		self:GetGraphicSettingTypeTbl().low,
		self:GetGraphicSettingTypeTbl().middle,
		self:GetGraphicSettingTypeTbl().high,
	}
	return self.transformGraphicSettingTypeTbl
end

function gameSetAdvancedManager:GetGraphicSettingTypeMax()
	return table.num_pairs(self:GetGraphicSettingTypeTbl())
end

function gameSetAdvancedManager:GetGraphicSetting()
	local graphic_setting = GameConfigMgr:GetGraphicsQuality()
	return graphic_setting
end

function gameSetAdvancedManager:SetGraphicSetting(graphic_setting)
	GameConfigMgr:SetGraphicsQuality(graphic_setting)
	self:LoadDefaultConfig(graphic_setting)
end
----------------------------------------------------------------------------------------------

-------------------------------------- 视野设置 -----------------------------------------------
function gameSetAdvancedManager:GetVDistanceTypeTbl()
	self.vDistanceTypeTbl = self.vDistanceTypeTbl or {
		near = 0,     -- 近
		middle = 1,   -- 中
		far = 2,      -- 远
		further = 3,  -- 更远
		farthest = 4, -- 最远
	}
	return self.vDistanceTypeTbl
end

function gameSetAdvancedManager:GetTransformVDistanceTypeTbl()
	self.transformVDistanceTypeTbl = self.transformVDistanceTypeTbl or {
		self:GetVDistanceTypeTbl().near,
		self:GetVDistanceTypeTbl().middle,
		self:GetVDistanceTypeTbl().far,
		self:GetVDistanceTypeTbl().further,
		self:GetVDistanceTypeTbl().farthest
	}
	return self.transformVDistanceTypeTbl
end

function gameSetAdvancedManager:GetVDistanceTypeMax()
	return table.num_pairs(self:GetVDistanceTypeTbl())
end

function gameSetAdvancedManager:GetVDistance()
	local view_distance = GameConfigMgr:GetVisionLevel()
	return view_distance
end

function gameSetAdvancedManager:SetVDistance(view_distance)
	GameConfigMgr:SetVisionLevel(view_distance)
end

-- 游戏中自动调整视野处理
function gameSetAdvancedManager:AutoSetVDistance(view_distance)
	if self:GetVDistance() == view_distance then
		return
	end
	self.nAutoSetVDistance = view_distance -- 自动调整视野后的值
	self:SetVDistance(view_distance)
	GetInst("gameSetAdvancedManager"):AppalyGameSetData(true)
	if isAbroadEvn() then
		GetInst("eventMgr"):dispatch("GAME_SET_ADVANCED_REFRESH")
	else
		g_LuaEvent:dispatchEvent(ET.GAME_SET_ADVANCED_REFRESH)
	end
end
----------------------------------------------------------------------------------------------

-------------------------------------- 雾效设置 -----------------------------------------------
function gameSetAdvancedManager:GetFogTypeTbl() -- 目前只有开关，后续可能会做扩展
	self.fogTypeTbl = self.fogTypeTbl or {
		none = 0, -- 关闭
		disable = 1, -- 失效
		linear = 2, -- 线性
		exp = 4,
		height = 8,
	}
	return self.fogTypeTbl
end

function gameSetAdvancedManager:GetFog()
	local fog = GameConfigMgr:GetFogEffect()
	return fog
end

function gameSetAdvancedManager:SetFog(fog)
	GetIWorldConfig():setGameData("fog", fog)
	GameConfigMgr:SetFogEffect(fog)
end
----------------------------------------------------------------------------------------------

-------------------------------------- 体积光设置 ---------------------------------------------
function gameSetAdvancedManager:GetGodray()
	local godray = GameConfigMgr:GetVolumetricLights()
	return godray
end

function gameSetAdvancedManager:SetGodray(godray)
	GameConfigMgr:SetVolumetricLights(godray)
end
----------------------------------------------------------------------------------------------

-------------------------------------- 颜色滤镜设置 -------------------------------------------
function gameSetAdvancedManager:GetColorFilter()
	local colorfilter = GameConfigMgr:GetLutFilter()
	return colorfilter
end

function gameSetAdvancedManager:SetColorFilter(colorfilter)
	GameConfigMgr:SetLutFilter(colorfilter)
end
----------------------------------------------------------------------------------------------

-------------------------------------- 泛光设置 -----------------------------------------------
function gameSetAdvancedManager:GetFloodlight()
	local floodlight = GameConfigMgr:GetBloom()
	return floodlight
end

function gameSetAdvancedManager:SetFloodlight(floodlight)
	GameConfigMgr:SetBloom(floodlight)
end
----------------------------------------------------------------------------------------------

-------------------------------------- 帧率类型 -----------------------------------------------
function gameSetAdvancedManager:GetFpsTypeTbl()
	self.fpsTypeTbl = self.fpsTypeTbl or {
		low = 30,     -- 低
		high = 60,    -- 高
		unlimited = 1 -- 不限
	}
	return self.fpsTypeTbl
end

function gameSetAdvancedManager:GetTransformFpsTypeTbl()
	self.transformFpsTypeTbl = self.transformFpsTypeTbl or {
		self:GetFpsTypeTbl().low,
		self:GetFpsTypeTbl().high,
		self:GetFpsTypeTbl().unlimited,
	}
	return self.transformFpsTypeTbl
end

function gameSetAdvancedManager:GetFpsTypeMax()
	return table.num_pairs(self:GetFpsTypeTbl())
end

-- 帧率设置
function gameSetAdvancedManager:GetFps()
	-- local fps_custom = GameConfigMgr:GetLimitFrameRate()
	local fps_custom = GetIWorldConfig():getGameData("fps_custom")
	if GetInst("gameSetAdvancedManager"):isMobile() and fps_custom == self:GetFpsTypeTbl().unlimited then
		fps_custom = self:GetFpsTypeTbl().high -- 移动端不开启不限制帧率选项
	end
	return fps_custom
end

function gameSetAdvancedManager:ConfigTransformSetFps(fps_custom)
	local limit_fps_config = {
		PC_LIMIT_ON = 80,
		PC_LIMIT_OFF = 240,
		MOBILE_LIMIT_ON = 30,
		MOBILE_LIMIT_OFF = 60,
	}
	if fps_custom == self:GetFpsTypeTbl().low then
		if GetInst("gameSetAdvancedManager"):isMobile() then
			return limit_fps_config.MOBILE_LIMIT_ON
		else
			return limit_fps_config.PC_LIMIT_ON
		end
	elseif fps_custom == self:GetFpsTypeTbl().high or fps_custom == self:GetFpsTypeTbl().unlimited then
		if GetInst("gameSetAdvancedManager"):isMobile() then
			return limit_fps_config.MOBILE_LIMIT_OFF
		else
			return limit_fps_config.PC_LIMIT_OFF
		end
	end
end

function gameSetAdvancedManager:SetFps(fps_custom)
	-- GameConfigMgr:SetLimitFrameRate(fps_custom)
	GetIWorldConfig():setGameData("fps_custom", fps_custom)
	if fps_custom == self:GetFpsTypeTbl().low then
		GetClientApp():SetLimitFPSFromLua(true, self:ConfigTransformSetFps(fps_custom))
	else
		GetClientApp():SetLimitFPSFromLua(false, self:ConfigTransformSetFps(fps_custom))
	end
end
----------------------------------------------------------------------------------------------

-------------------------------------- 帧率显示设置 --------------------------------------------
function gameSetAdvancedManager:GetFpsShow()
	local fps_show = GetIWorldConfig():getGameData("fpsbuttom")
	return fps_show
end

function gameSetAdvancedManager:SetFpsShow(fps_show)
	GetIWorldConfig():setGameData("fpsbuttom", fps_show)
end
----------------------------------------------------------------------------------------------

-------------------------------------- 树(方形)设置 --------------------------------------------
function gameSetAdvancedManager:GetTreeShapeTypeTbl()
	self.treeShapeTypeTbl = self.treeShapeTypeTbl or {
		square = 0, -- 方形
		circle = 1, -- 圆形
	}
	return self.treeShapeTypeTbl
end

function gameSetAdvancedManager:GetTransformTreeShapeTypeTbl()
	self.transformTreeShapeTypeTbl = self.transformTreeShapeTypeTbl or {
		self:GetTreeShapeTypeTbl().square,
		self:GetTreeShapeTypeTbl().circle,
	}
	return self.transformTreeShapeTypeTbl
end

function gameSetAdvancedManager:GetTreeShapeTypeMax()
	return table.num_pairs(self:GetTreeShapeTypeTbl())
end

function gameSetAdvancedManager:GetTreeShape()
	local treeshape = GetIWorldConfig():getGameData("treeshape")
	return treeshape
end

function gameSetAdvancedManager:SetTreeShape(treeshape)
	GetIWorldConfig():setGameData("treeshape", treeshape)
end
----------------------------------------------------------------------------------------------

-------------------------------------- 方块形状设置 --------------------------------------------
function gameSetAdvancedManager:GetBlockShapeTypeTbl()
	self.blockShapeTypeTbl = self.blockShapeTypeTbl or {
		round_angle = 0, -- 圆角
		right_angle = 1, -- 直角
	}
	return self.blockShapeTypeTbl
end

function gameSetAdvancedManager:GetTransformBlockShapeTypeTbl()
	self.transformBlockShapeTypeTbl = self.transformBlockShapeTypeTbl or {
		self:GetBlockShapeTypeTbl().round_angle,
		self:GetBlockShapeTypeTbl().right_angle,
	}
	return self.transformBlockShapeTypeTbl
end

function gameSetAdvancedManager:GetBlockShapeTypeMax()
	return table.num_pairs(self:GetBlockShapeTypeTbl())
end

function gameSetAdvancedManager:GetBlockShape()
	local blockshape = GetIWorldConfig():getGameData("blockshape")
	return blockshape
end

function gameSetAdvancedManager:SetBlockShape(blockshape)
	GetIWorldConfig():setGameData("blockshape", blockshape)
end
----------------------------------------------------------------------------------------------

-------------------------------------- 震动设置 -----------------------------------------------
function gameSetAdvancedManager:GetVibrate()
	local vibrate = GetIWorldConfig():getGameData("vibrate")
	return vibrate
end

function gameSetAdvancedManager:SetVibrate(vibrate)
	GetIWorldConfig():setGameData("vibrate", vibrate)
end
----------------------------------------------------------------------------------------------

-------------------------------------- 阴影设置 -----------------------------------------------
function gameSetAdvancedManager:GetShadow()
	-- local shadow = GetIWorldConfig():getGameData("shadow")
	local shadow = GameConfigMgr:GetRealTimeShadows()
	return shadow
end

function gameSetAdvancedManager:SetShadow(shadow)
	-- GetIWorldConfig():setGameData("shadow", shadow)
	GameConfigMgr:SetRealTimeShadows(shadow)
end
----------------------------------------------------------------------------------------------

-------------------------------------- 水反设置 -----------------------------------------------
function gameSetAdvancedManager:GetReflect()
	-- local reflect = GetIWorldConfig():getGameData("reflect")
	local reflect = GameConfigMgr:GetWaterReflection()
	return reflect
end

function gameSetAdvancedManager:SetReflect(reflect)
	-- GetIWorldConfig():setGameData("reflect", reflect)
	GameConfigMgr:SetWaterReflection(reflect)
end
----------------------------------------------------------------------------------------------

-------------------------------------- 动态光源设置 -----------------------------------------------
function gameSetAdvancedManager:GetDynamicLights()
	local dynamicLight = GetIWorldConfig():getGameData("dynamicLights")
	return dynamicLight
end

function gameSetAdvancedManager:SetDynamicLights(dynamicLights)
	GetIWorldConfig():setGameData("dynamicLights", dynamicLights)
	if dynamicLights == GetInst("gameSetAdvancedManager"):GetSwitchTypeTbl().open then
		self:SetLocalLight(true)
	else
		self:SetLocalLight(false)
	end
end

function gameSetAdvancedManager:SetLocalLight(dynamicLights)
	if GameConfigMgr and GameConfigMgr.SetLocalLight then
		GameConfigMgr:SetLocalLight(dynamicLights)
	end
end

-- 刷新设置界面ui（目前用于studio地图配置重载）
function gameSetAdvancedManager:StudioLoadMapCustomConfig()
	local ctrl = GetInst("MiniUIManager"):GetCtrl("gameSetAdvanced")
	if ctrl then
		ctrl:StudioLoadMapCustomConfigRefreshView()
	end
end

----------------------------------------------------------------------------------------------

-------------------------------------- 视野自动调整开关设置 ---------------------------------------
function gameSetAdvancedManager:GetAutoSetViewDistance()
	local autoSetViewDistance = GetIWorldConfig():getGameData("autoSetViewDistance")
	return autoSetViewDistance
end

function gameSetAdvancedManager:SetAutoSetViewDistance(autoSetViewDistance)
	GetIWorldConfig():setGameData("autoSetViewDistance", autoSetViewDistance)
end
----------------------------------------------------------------------------------------------

function gameSetAdvancedManager:AppalyGameSetData(bl)
	GetClientInfo():appalyGameSetData(bl)
end

return gameSetAdvancedManager
